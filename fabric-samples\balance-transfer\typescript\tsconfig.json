{"compilerOptions": {"removeComments": false, "preserveConstEnums": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "declaration": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "suppressImplicitAnyIndexErrors": true, "moduleResolution": "node", "module": "commonjs", "target": "es6", "outDir": "dist", "baseUrl": ".", "typeRoots": ["types", "node_modules/@types"]}, "formatCodeOptions": {"indentSize": 2, "tabSize": 2}}