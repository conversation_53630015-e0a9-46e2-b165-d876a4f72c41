name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [12.22.12]
    
    services:
      docker:
        image: docker:20.10.16
        options: --privileged
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: fabcar-app/enhanced-client/package-lock.json
    
    - name: Install dependencies
      run: |
        cd fabcar-app/enhanced-client
        npm ci
    
    - name: Lint code
      run: |
        cd fabcar-app/enhanced-client
        npm run lint || echo "Linting not configured"
    
    - name: Run unit tests
      run: |
        cd fabcar-app/enhanced-client
        npm test || echo "Tests not configured"
    
    - name: Build Docker image
      run: |
        cd fabcar-app/enhanced-client
        docker build -t fabcar-api:test .
    
    - name: Test Docker image
      run: |
        docker run --rm fabcar-api:test node --version

  security:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Run security audit
      run: |
        cd fabcar-app/enhanced-client
        npm audit --audit-level moderate || true
    
    - name: Scan for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD

  documentation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Check README
      run: |
        if [ ! -f README.md ]; then
          echo "README.md not found"
          exit 1
        fi
        echo "README.md exists"
    
    - name: Validate markdown
      uses: DavidAnson/markdownlint-cli2-action@v9
      with:
        globs: '**/*.md'
