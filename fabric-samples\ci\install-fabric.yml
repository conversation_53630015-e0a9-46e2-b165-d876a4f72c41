#
# SPDX-License-Identifier: Apache-2.0
#

steps:
  - script: |
      set -eo pipefail
      curl -L --retry 5 --retry-delay 3 https://hyperledger.jfrog.io/hyperledger/fabric-binaries/hyperledger-fabric-linux-amd64-1.4-stable.tar.gz | tar xz
    displayName: Download Fabric CLI
  - script: |
      set -eo pipefail
      curl -L --retry 5 --retry-delay 3 https://hyperledger.jfrog.io/hyperledger/fabric-binaries/hyperledger-fabric-ca-linux-amd64-1.4-stable.tar.gz | tar xz
    displayName: Download Fabric CA CLI
  - script: |
      set -ex
      version=1.4
      for i in ca ccenv javaenv peer orderer tools; do
        docker pull hyperledger-fabric.jfrog.io/fabric-$i:amd64-$version-stable
        docker tag hyperledger-fabric.jfrog.io/fabric-$i:amd64-$version-stable hyperledger/fabric-$i:amd64-$version-stable
        docker tag hyperledger-fabric.jfrog.io/fabric-$i:amd64-$version-stable hyperledger/fabric-$i:amd64-$version
        docker tag hyperledger-fabric.jfrog.io/fabric-$i:amd64-$version-stable hyperledger/fabric-$i:$version
        docker tag hyperledger-fabric.jfrog.io/fabric-$i:amd64-$version-stable hyperledger/fabric-$i:latest
      done

      docker pull hyperledger/fabric-couchdb:0.4.15
      docker tag hyperledger/fabric-couchdb:0.4.15 hyperledger/fabric-couchdb:latest
    displayName: Pull Fabric Docker images
