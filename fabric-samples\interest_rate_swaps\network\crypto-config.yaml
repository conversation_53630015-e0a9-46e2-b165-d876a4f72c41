# Copyright IBM Corp. All Rights Reserved.
#
# SPDX-License-Identifier: Apache-2.0
#

OrdererOrgs:
  - Name: orderer
    Domain: example.com
    Specs:
      - Hostname: orderer

PeerOrgs:
  - Name: partya
    Domain: partya.example.com
    EnableNodeOUs: true
    Template:
      Count: 1
    Users:
      Count: 1

  - Name: partyb
    Domain: partyb.example.com
    EnableNodeOUs: true
    Template:
      Count: 1
    Users:
      Count: 1

  - Name: partyc
    Domain: partyc.example.com
    EnableNodeOUs: true
    Template:
      Count: 1
    Users:
      Count: 1

  - Name: auditor
    Domain: auditor.example.com
    EnableNodeOUs: true
    Template:
      Count: 1
    Users:
      Count: 1

  - Name: rrprovider
    Domain: rrprovider.example.com
    EnableNodeOUs: true
    Template:
      Count: 1
    Users:
      Count: 1
