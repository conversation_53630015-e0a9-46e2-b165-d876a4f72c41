<project xmlns="http://maven.apache.org/POM/4.0.0"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
   <modelVersion>4.0.0</modelVersion>
   <groupId>fabcar-java</groupId>
   <artifactId>fabcar-java</artifactId>
   <version>1.4.0-SNAPSHOT</version>
   <build>
      <plugins>
         <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.0</version>
            <configuration>
               <source>1.8</source>
               <target>1.8</target>
            </configuration>
         </plugin>
      </plugins>
   </build>
   <repositories>
      <repository>
         <id>oss-sonatype</id>
         <name>OSS Sonatype</name>
         <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      </repository>
   </repositories>
   <dependencies>
      <dependency>
         <groupId>org.hyperledger.fabric</groupId>
         <artifactId>fabric-gateway-java</artifactId>
         <version>1.4.5</version>
      </dependency>
      <dependency>
         <groupId>org.junit.platform</groupId>
         <artifactId>junit-platform-launcher</artifactId>
         <version>1.4.2</version>
      </dependency>
      <dependency>
         <groupId>org.junit.jupiter</groupId>
         <artifactId>junit-jupiter-engine</artifactId>
         <version>5.4.1</version>
         <scope>test</scope>
      </dependency>
      <dependency>
         <groupId>org.junit.vintage</groupId>
         <artifactId>junit-vintage-engine</artifactId>
         <version>5.4.2</version>
      </dependency>
      <dependency>
         <groupId>org.assertj</groupId>
         <artifactId>assertj-core</artifactId>
         <version>3.12.2</version>
         <scope>test</scope>
      </dependency>
   </dependencies>
</project>
