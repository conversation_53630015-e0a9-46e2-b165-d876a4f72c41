{"network-config": {"orderer": {"url": "grpcs://localhost:7050", "server-hostname": "orderer.example.com", "tls_cacerts": "../artifacts/channel/crypto-config/ordererOrganizations/example.com/orderers/orderer.example.com/tls/ca.crt"}, "org1": {"name": "peerOrg1", "mspid": "Org1MSP", "ca": "https://localhost:7054", "peers": {"peer1": {"requests": "grpcs://localhost:7051", "events": "grpcs://localhost:7053", "server-hostname": "peer0.org1.example.com", "tls_cacerts": "../artifacts/channel/crypto-config/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt"}, "peer2": {"requests": "grpcs://localhost:7056", "events": "grpcs://localhost:7058", "server-hostname": "peer1.org1.example.com", "tls_cacerts": "../artifacts/channel/crypto-config/peerOrganizations/org1.example.com/peers/peer1.org1.example.com/tls/ca.crt"}}, "admin": {"key": "../artifacts/channel/crypto-config/peerOrganizations/org1.example.com/users/<EMAIL>/msp/keystore", "cert": "../artifacts/channel/crypto-config/peerOrganizations/org1.example.com/users/<EMAIL>/msp/signcerts"}}, "org2": {"name": "peerOrg2", "mspid": "Org2MSP", "ca": "https://localhost:8054", "peers": {"peer1": {"requests": "grpcs://localhost:8051", "events": "grpcs://localhost:8053", "server-hostname": "peer0.org2.example.com", "tls_cacerts": "../artifacts/channel/crypto-config/peerOrganizations/org2.example.com/peers/peer0.org2.example.com/tls/ca.crt"}, "peer2": {"requests": "grpcs://localhost:8056", "events": "grpcs://localhost:8058", "server-hostname": "peer1.org2.example.com", "tls_cacerts": "../artifacts/channel/crypto-config/peerOrganizations/org2.example.com/peers/peer1.org2.example.com/tls/ca.crt"}}, "admin": {"key": "../artifacts/channel/crypto-config/peerOrganizations/org2.example.com/users/<EMAIL>/msp/keystore", "cert": "../artifacts/channel/crypto-config/peerOrganizations/org2.example.com/users/<EMAIL>/msp/signcerts"}}}}