#
# SPDX-License-Identifier: Apache-2.0
#

trigger:
  - master
  - release-1.4

variables:
  NODE_VER: '10.x'
  PATH: $(Agent.BuildDirectory)/bin:/bin:/usr/bin:/sbin:/usr/sbin:/usr/local/bin:/usr/local/sbin

jobs:
  - job: fabcar_go
    displayName: FabCar (Go)
    pool:
      vmImage: ubuntu-18.04
    dependsOn: []
    timeoutInMinutes: 60
    steps:
      - template: install-deps.yml
      - template: install-fabric.yml
      - template: fabcar-go.yml

  - job: fabcar_java
    displayName: FabCar (Java)
    pool:
      vmImage: ubuntu-18.04
    dependsOn: []
    timeoutInMinutes: 60
    steps:
      - template: install-deps.yml
      - template: install-fabric.yml
      - template: fabcar-java.yml

  - job: fabcar_javascript
    displayName: FabCar (JavaScript)
    pool:
      vmImage: ubuntu-18.04
    dependsOn: []
    timeoutInMinutes: 60
    steps:
      - template: install-deps.yml
      - template: install-fabric.yml
      - template: fabcar-javascript.yml

  - job: fabcar_typescript
    displayName: FabCar (TypeScript)
    pool:
      vmImage: ubuntu-18.04
    dependsOn: []
    timeoutInMinutes: 60
    steps:
      - template: install-deps.yml
      - template: install-fabric.yml
      - template: fabcar-typescript.yml
