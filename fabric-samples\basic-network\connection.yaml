---
name: basic-network
version: 1.0.0
client:
    organization: Org1
    connection:
        timeout:
            peer:
                endorser: '300'
            orderer: '300'
channels:
    mychannel:
        orderers:
        - orderer.example.com
        peers:
            peer0.org1.example.com: {}
organizations:
    Org1:
        mspid: Org1MSP
        peers:
        - peer0.org1.example.com
        certificateAuthorities:
        - ca.example.com
orderers:
    orderer.example.com:
        url: grpc://localhost:7050
peers:
    peer0.org1.example.com:
        url: grpc://localhost:7051
certificateAuthorities:
    ca.example.com:
        url: http://localhost:7054
        caName: ca.example.com
