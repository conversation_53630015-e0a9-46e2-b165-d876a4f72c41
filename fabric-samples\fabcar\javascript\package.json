{"name": "fabcar", "version": "1.0.0", "description": "FabCar application implemented in JavaScript", "engines": {"node": ">=8", "npm": ">=5"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --recursive"}, "engineStrict": true, "author": "Hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-ca-client": "~1.4.0", "fabric-network": "~1.4.0"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^5.9.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "sinon": "^7.1.1", "sinon-chai": "^3.3.0"}, "nyc": {"exclude": ["coverage/**", "test/**"], "reporter": ["text-summary", "html"], "all": true, "check-coverage": true, "statements": 100, "branches": 100, "functions": 100, "lines": 100}}