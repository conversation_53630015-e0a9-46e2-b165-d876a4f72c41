{"extends": "tslint:recommended", "rulesDirectory": ["tslint-microsoft-contrib"], "rules": {"trailing-comma": [false, {"multiline": "always", "singleline": "never"}], "interface-name": [false, "always-prefix"], "no-console": [true, "time", "timeEnd", "trace"], "max-line-length": [true, 100], "no-string-literal": false, "no-use-before-declare": true, "object-literal-sort-keys": false, "ordered-imports": [false], "quotemark": [true, "single", "avoid-escape"], "variable-name": [true, "allow-leading-underscore", "allow-pascal-case", "ban-keywords", "check-format"]}}