{"name": "fabcar-enhanced-client", "version": "1.0.0", "description": "Enhanced FabCar blockchain client application with modern dependencies", "main": "app.js", "scripts": {"start": "node app.js", "enroll": "node enrollAdmin.js", "register": "node registerUser.js", "query": "node query.js", "invoke": "node invoke.js", "test": "node test.js"}, "dependencies": {"fabric-network": "^2.2.20", "fabric-ca-client": "^2.2.20", "express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "winston": "^3.8.2"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["hyperledger", "fabric", "blockchain", "fabcar", "nodejs"], "author": "FabCar Development Team", "license": "Apache-2.0", "engines": {"node": ">=14.0.0"}}