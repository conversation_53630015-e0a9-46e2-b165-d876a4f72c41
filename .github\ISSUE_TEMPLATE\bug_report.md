---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Windows 10, macOS 12, Ubuntu 20.04]
 - Node.js version: [e.g. 12.22.12]
 - Docker version: [e.g. 20.10.16]
 - Docker Compose version: [e.g. 1.29.2]

**Blockchain Network Information:**
 - Network status: [running/stopped/error]
 - Number of containers running: [e.g. 7]
 - Chaincode status: [installed/instantiated/error]

**Logs**
If applicable, add relevant log outputs:
```
Paste log output here
```

**Additional context**
Add any other context about the problem here.
