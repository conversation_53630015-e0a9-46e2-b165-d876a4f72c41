{"name": "balance-transfer", "version": "1.0.0", "description": "A balance-transfer example node program to demonstrate using node.js SDK APIs", "main": "app.js", "scripts": {"start": "node app.js"}, "keywords": ["fabric-client sample app", "balance-transfer node sample", "v1.0 fabric nodesdk sample"], "engines": {"node": ">=8.9.4 <9.0", "npm": ">=5.6.0 <6.0"}, "license": "Apache-2.0", "dependencies": {"body-parser": "^1.17.1", "cookie-parser": "^1.4.3", "cors": "^2.8.3", "express": "^4.15.2", "express-bearer-token": "^2.1.0", "express-jwt": "^5.1.0", "express-session": "^1.15.2", "fabric-ca-client": "~1.4.0", "fabric-client": "~1.4.0", "fs-extra": "^2.0.0", "jsonwebtoken": "^7.3.0", "log4js": "^0.6.38"}}